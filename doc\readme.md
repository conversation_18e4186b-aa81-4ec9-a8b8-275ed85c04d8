# APB UVM验证平台

## 项目概述

本项目是一个基于UVM方法学的APB（Advanced Peripheral Bus）验证平台，包含完整的DUT设计和验证环境。

## 目录结构

```
uvm_apb/
├── dut/                    # 设计文件
│   └── rtl/
│       └── apb_slave.v     # APB从设备RTL设计
├── tb/                     # 测试平台
│   ├── agents/             # UVM Agent
│   │   └── apb_agent/      # APB Agent组件
│   │       ├── apb_if.sv           # APB接口
│   │       ├── apb_seq_item.sv     # 序列项
│   │       ├── apb_driver.sv       # 驱动器
│   │       ├── apb_monitor.sv      # 监视器
│   │       ├── apb_sequencer.sv    # 序列器
│   │       └── apb_agent.sv        # 代理
│   ├── sequences/          # 测试序列
│   │   ├── apb_base_seq.sv         # 基础序列
│   │   ├── apb_write_seq.sv        # 写序列
│   │   ├── apb_read_seq.sv         # 读序列
│   │   └── apb_read_write_seq.sv   # 读写混合序列
│   ├── env/                # 测试环境
│   │   ├── apb_env.sv              # 环境容器
│   │   └── apb_scoreboard.sv       # 记分板
│   ├── tests/              # 测试用例
│   │   ├── apb_pkg.sv              # UVM包文件
│   │   ├── apb_base_test.sv        # 基础测试
│   │   └── apb_read_write_test.sv  # 读写测试
│   └── top/                # 顶层文件
│       └── tb_top.sv               # 测试平台顶层
├── sim/                    # 仿真脚本
│   ├── Makefile                    # 编译仿真脚本
│   └── filelist.f                  # 文件列表
└── doc/                    # 文档
    └── readme.md                   # 说明文档
```

## DUT设计说明

APB Slave是一个简单的APB从设备，具有以下特性：
- 包含4个32位寄存器（地址0x00, 0x04, 0x08, 0x0C）
- 支持标准APB协议（SETUP和ACCESS阶段）
- 支持字节选通写入和完整字读取
- 支持字对齐访问

## 验证环境说明

### UVM组件
- **APB Agent**: 包含driver、monitor、sequencer的完整agent
- **APB Interface**: 定义APB信号和时钟块
- **APB Sequence Item**: 定义APB事务的数据结构
- **APB Sequences**: 提供不同的测试序列（写、读、读写混合）
- **APB Scoreboard**: 包含参考模型和数据检查逻辑
- **APB Environment**: 整合所有验证组件

### 测试用例
- **apb_base_test**: 基础写测试
- **apb_read_write_test**: 读写混合测试

## 使用方法

### 环境要求
- VCS仿真器
- LSF作业调度系统
- Verdi波形查看器（可选）

### 仿真运行

```bash
# 进入仿真目录
cd sim

# 基本测试（编译+仿真，自动提交LSF）
make all TEST_NAME=apb_base_test SEED=1

# 读写测试
make all TEST_NAME=apb_read_write_test SEED=5262248366 CMODEL=1

# 只编译（提交LSF）
make compile

# 只仿真（提交LSF）
make run TEST_NAME=apb_base_test SEED=1

# 回归测试
make regression

# 查看波形
make wave

# 清理生成文件
make clean

# 查看帮助
make help
```

### 参数说明
- **TEST_NAME**: 测试用例名称（默认：apb_base_test）
- **SEED**: 随机种子（默认：1）
- **CMODEL**: C模型使能（默认：0）
- **VERBOSITY**: UVM详细程度（默认：UVM_MEDIUM）

### 可用测试用例
- `apb_base_test`: 基础写操作测试
- `apb_read_write_test`: 读写混合操作测试

## 注意事项

1. **LSF环境**: 所有编译和仿真都会自动提交到LSF队列（analog.q）
2. **资源配置**: 使用2个CPU核心和4GB内存
3. **波形生成**: 默认生成FSDB格式波形文件
4. **超时保护**: 仿真设置1ms超时保护

## 扩展说明

如需添加新的测试用例：
1. 在`tb/sequences/`目录下创建新的序列文件
2. 在`tb/tests/`目录下创建新的测试文件
3. 在`tb/tests/apb_pkg.sv`中包含新文件
4. 更新Makefile中的帮助信息

## 联系信息

如有问题请联系验证团队。