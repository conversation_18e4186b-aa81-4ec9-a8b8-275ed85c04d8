import os

# 定义目录和文件的结构
# 字典的键是文件路径，值是写入文件的初始注释内容
# 对于只需要创建空文件的，值可以设为 None 或空字符串
file_structure = {
    "dut/rtl/apb_slave.v": "// APB slave RTL设计",
    "tb/agents/apb_agent/apb_if.sv": "// APB接口定义",
    "tb/agents/apb_agent/apb_seq_item.sv": "// 序列项",
    "tb/agents/apb_agent/apb_driver.sv": "// 驱动器",
    "tb/agents/apb_agent/apb_monitor.sv": "// 监视器",
    "tb/agents/apb_agent/apb_sequencer.sv": "// 序列器",
    "tb/agents/apb_agent/apb_agent.sv": "// 代理",
    "tb/env/apb_scoreboard.sv": "// 记分板",
    "tb/env/apb_env.sv": "// 环境容器",
    "tb/sequences/apb_base_seq.sv": "// 基础序列",
    "tb/sequences/apb_read_seq.sv": "// 读序列",
    "tb/sequences/apb_write_seq.sv": "// 写序列",
    "tb/sequences/apb_read_write_seq.sv": "// 读写混合序列",
    "tb/tests/apb_pkg.sv": "// 包文件",
    "tb/tests/apb_base_test.sv": "// 基础测试",
    "tb/tests/apb_read_write_test.sv": "// 读写测试",
    "tb/top/tb_top.sv": "// 测试平台顶层",
    "sim/Makefile": "# 编译仿真脚本",
    "sim/filelist.f": None,
    "doc/readme.md": "# 本文档"
}

def create_project_structure():
    """
    根据定义的结构创建目录和文件
    """
    # 首先创建那个要求为空的 out 目录
    try:
        os.makedirs("sim/out", exist_ok=True)
        print("目录创建成功: sim/out")
    except OSError as e:
        print(f"创建目录 sim/out 失败: {e}")
        return

    # 遍历字典，创建其他文件和它们所在的目录
    for file_path, content in file_structure.items():
        # 获取文件所在的目录路径
        dir_name = os.path.dirname(file_path)

        try:
            # 如果目录不为空，则创建目录
            if dir_name:
                os.makedirs(dir_name, exist_ok=True)

            # 创建文件并写入内容
            with open(file_path, 'w', encoding='utf-8') as f:
                if content:
                    f.write(content + '\n')
            print(f"文件创建成功: {file_path}")

        except OSError as e:
            print(f"创建 {file_path} 或其目录时失败: {e}")
            break # 如果出错则停止

if __name__ == "__main__":
    print("开始创建UVM项目结构...")
    create_project_structure()
    print("\n目录结构和文件创建成功！")
    # (可选) 如果你希望在脚本最后看到目录树，可以尝试执行系统命令
    # 这需要 'tree' 命令已经安装在你的系统中
    # if os.name == 'posix': # for Linux/macOS
    #     os.system('tree')
    # elif os.name == 'nt': # for Windows
    #     os.system('tree /F')