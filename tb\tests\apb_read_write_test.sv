// APB读写测试
import uvm_pkg::*;
import apb_pkg::*;
`include "uvm_macros.svh"

class apb_read_write_test extends apb_base_test;

    // UVM宏
    `uvm_component_utils(apb_read_write_test)

    // 构造函数
    function new(string name = "apb_read_write_test", uvm_component parent = null);
        super.new(name, parent);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);

        // 设置读写混合序列
        uvm_config_db#(uvm_object_wrapper)::set(this, "env.agent.sequencer.main_phase",
                                                "default_sequence", apb_read_write_seq::type_id::get());
    endfunction

    // start_of_simulation_phase
    virtual function void start_of_simulation_phase(uvm_phase phase);
        super.start_of_simulation_phase(phase);
        `uvm_info("APB_RW_TEST", "Starting APB Read Write Test", UVM_LOW)
    endfunction

endclass
