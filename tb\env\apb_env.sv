// APB环境
class apb_env extends uvm_env;

    // 组件
    apb_agent      agent;
    apb_scoreboard scoreboard;

    // UVM宏
    `uvm_component_utils(apb_env)

    // 构造函数
    function new(string name = "apb_env", uvm_component parent = null);
        super.new(name, parent);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);

        // 创建组件
        agent = apb_agent::type_id::create("agent", this);
        scoreboard = apb_scoreboard::type_id::create("scoreboard", this);

        // 设置agent为active模式
        uvm_config_db#(uvm_active_passive_enum)::set(this, "agent", "is_active", UVM_ACTIVE);
    endfunction

    // connect_phase
    virtual function void connect_phase(uvm_phase phase);
        super.connect_phase(phase);

        // 连接agent的分析端口到scoreboard
        agent.ap.connect(scoreboard.item_collected_export);
    endfunction

endclass
