// APB读写混合序列
import uvm_pkg::*;
`include "uvm_macros.svh"

class apb_read_write_seq extends apb_base_seq;

    // UVM宏
    `uvm_object_utils(apb_read_write_seq)

    // 构造函数
    function new(string name = "apb_read_write_seq");
        super.new(name);
    endfunction

    // 主体任务
    virtual task body();
        apb_seq_item write_req, read_req;
        bit [31:0] write_addr;

        // 先写后读测试
        repeat(5) begin
            // 写操作
            write_req = apb_seq_item::type_id::create("write_req");
            start_item(write_req);
            if (!write_req.randomize() with {
                pwrite == 1'b1;
            }) begin
                `uvm_error("APB_RW_SEQ", "Write randomization failed")
            end
            write_addr = write_req.paddr;  // 保存写地址
            `uvm_info("APB_RW_SEQ", $sformatf("Sending write: %s", write_req.convert2string()), UVM_MEDIUM)
            finish_item(write_req);

            // 读操作（相同地址）
            read_req = apb_seq_item::type_id::create("read_req");
            start_item(read_req);
            if (!read_req.randomize() with {
                pwrite == 1'b0;
                paddr == write_addr;  // 读取相同地址
            }) begin
                `uvm_error("APB_RW_SEQ", "Read randomization failed")
            end
            `uvm_info("APB_RW_SEQ", $sformatf("Sending read: %s", read_req.convert2string()), UVM_MEDIUM)
            finish_item(read_req);
        end
    endtask

endclass
