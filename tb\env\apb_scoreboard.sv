// APB记分板
class apb_scoreboard extends uvm_scoreboard;

    // 分析端口
    uvm_analysis_export #(apb_seq_item) item_collected_export;
    uvm_tlm_analysis_fifo #(apb_seq_item) item_collected_fifo;

    // UVM宏
    `uvm_component_utils(apb_scoreboard)

    // 构造函数
    function new(string name = "apb_scoreboard", uvm_component parent = null);
        super.new(name, parent);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);

        // 创建分析端口和FIFO
        item_collected_export = new("item_collected_export", this);
        item_collected_fifo = new("item_collected_fifo", this);
    endfunction

    // connect_phase
    virtual function void connect_phase(uvm_phase phase);
        super.connect_phase(phase);

        // 连接export到fifo
        item_collected_export.connect(item_collected_fifo.analysis_export);
    endfunction

    // run_phase
    virtual task run_phase(uvm_phase phase);
        apb_seq_item item;

        forever begin
            // 从FIFO获取事务
            item_collected_fifo.get(item);

            // 检查事务
            check_transaction(item);
        end
    endtask

    // 检查事务
    virtual function void check_transaction(apb_seq_item item);
        `uvm_info("APB_SCOREBOARD", $sformatf("收到事务: %s", item.convert2string()), UVM_MEDIUM)

        // 这里可以添加具体的检查逻辑
        // 例如：检查读写数据的一致性等
    endfunction

endclass
