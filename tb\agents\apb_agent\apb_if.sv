// APB接口定义
interface apb_if (input logic pclk, input logic presetn);

    // APB信号定义
    logic        psel;
    logic        penable;
    logic        pwrite;
    logic [31:0] paddr;
    logic [31:0] pwdata;
    logic [3:0]  pstrb;
    logic [31:0] prdata;
    logic        pready;
    logic        pslverr;

    // 时钟块定义 - 用于driver
    clocking driver_cb @(posedge pclk);
        default input #1step output #1step;
        output psel, penable, pwrite, paddr, pwdata, pstrb;
        input  prdata, pready, pslverr;
    endclocking

    // 时钟块定义 - 用于monitor
    clocking monitor_cb @(posedge pclk);
        default input #1step;
        input psel, penable, pwrite, paddr, pwdata, pstrb;
        input prdata, pready, pslverr;
    endclocking

    // modport定义
    modport driver_mp (clocking driver_cb, input pclk, presetn);
    modport monitor_mp (clocking monitor_cb, input pclk, presetn);
    modport dut_mp (
        input  psel, penable, pwrite, paddr, pwdata, pstrb,
        output prdata, pready, pslverr
    );

    // 复位任务
    task reset();
        psel    <= 1'b0;
        penable <= 1'b0;
        pwrite  <= 1'b0;
        paddr   <= 32'h0;
        pwdata  <= 32'h0;
        pstrb   <= 4'h0;
    endtask

    // 等待复位释放
    task wait_reset_release();
        wait(presetn === 1'b1);
        @(posedge pclk);
    endtask

endinterface
