// APB slave RTL设计
// 简单的APB从设备，包含4个32位寄存器

module apb_slave (
    input  wire        pclk,
    input  wire        presetn,
    input  wire        psel,
    input  wire        penable,
    input  wire        pwrite,
    input  wire [31:0] paddr,
    input  wire [31:0] pwdata,
    input  wire [3:0]  pstrb,
    output reg  [31:0] prdata,
    output reg         pready,
    output wire        pslverr
);

    // 内部寄存器组 - 4个32位寄存器
    reg [31:0] reg_array [0:3];

    // 地址解码
    wire [1:0] reg_addr = paddr[3:2];

    // 错误信号 - 简单实现，暂不产生错误
    assign pslverr = 1'b0;

    // APB状态机
    typedef enum logic [1:0] {
        IDLE   = 2'b00,
        SETUP  = 2'b01,
        ACCESS = 2'b10
    } apb_state_t;

    apb_state_t current_state, next_state;

    // 状态机时序逻辑
    always_ff @(posedge pclk or negedge presetn) begin
        if (!presetn) begin
            current_state <= IDLE;
        end else begin
            current_state <= next_state;
        end
    end

    // 状态机组合逻辑
    always_comb begin
        case (current_state)
            IDLE: begin
                if (psel && !penable)
                    next_state = SETUP;
                else
                    next_state = IDLE;
            end
            SETUP: begin
                if (psel && penable)
                    next_state = ACCESS;
                else
                    next_state = IDLE;
            end
            ACCESS: begin
                if (psel && penable)
                    next_state = ACCESS;
                else
                    next_state = IDLE;
            end
            default: next_state = IDLE;
        endcase
    end

    // 寄存器读写逻辑
    always_ff @(posedge pclk or negedge presetn) begin
        if (!presetn) begin
            reg_array[0] <= 32'h0;
            reg_array[1] <= 32'h0;
            reg_array[2] <= 32'h0;
            reg_array[3] <= 32'h0;
            prdata <= 32'h0;
            pready <= 1'b0;
        end else begin
            case (current_state)
                IDLE: begin
                    pready <= 1'b0;
                    prdata <= 32'h0;
                end
                SETUP: begin
                    pready <= 1'b0;
                end
                ACCESS: begin
                    pready <= 1'b1;
                    if (pwrite) begin
                        // 写操作
                        if (pstrb[0]) reg_array[reg_addr][7:0]   <= pwdata[7:0];
                        if (pstrb[1]) reg_array[reg_addr][15:8]  <= pwdata[15:8];
                        if (pstrb[2]) reg_array[reg_addr][23:16] <= pwdata[23:16];
                        if (pstrb[3]) reg_array[reg_addr][31:24] <= pwdata[31:24];
                    end else begin
                        // 读操作
                        prdata <= reg_array[reg_addr];
                    end
                end
            endcase
        end
    end

endmodule
