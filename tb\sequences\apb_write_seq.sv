// APB写序列
import uvm_pkg::*;
`include "uvm_macros.svh"

class apb_write_seq extends apb_base_seq;

    // UVM宏
    `uvm_object_utils(apb_write_seq)

    // 构造函数
    function new(string name = "apb_write_seq");
        super.new(name);
    endfunction

    // 主体任务
    virtual task body();
        apb_seq_item req;

        repeat(10) begin
            req = apb_seq_item::type_id::create("req");
            start_item(req);

            // 随机化写事务
            if (!req.randomize() with {
                pwrite == 1'b1;
            }) begin
                `uvm_error("APB_WRITE_SEQ", "Randomization failed")
            end

            `uvm_info("APB_WRITE_SEQ", $sformatf("Sending write: %s", req.convert2string()), UVM_MEDIUM)
            finish_item(req);
        end
    endtask

endclass
