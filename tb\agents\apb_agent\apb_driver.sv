// APB驱动器

class apb_driver extends uvm_driver #(apb_seq_item);

    // 虚拟接口
    virtual apb_if vif;

    // UVM宏
    `uvm_component_utils(apb_driver)

    // 构造函数
    function new(string name = "apb_driver", uvm_component parent = null);
        super.new(name, parent);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        if (!uvm_config_db#(virtual apb_if)::get(this, "", "vif", vif)) begin
            `uvm_fatal("NOVIF", "Virtual interface must be set for apb_driver")
        end
    endfunction

    // run_phase
    virtual task run_phase(uvm_phase phase);
        apb_seq_item req;

        // 初始化信号
        `uvm_info("APB_DRIVER", "Initializing APB signals", UVM_LOW)
        vif.reset();
        `uvm_info("APB_DRIVER", "Waiting for reset release", UVM_LOW)
        vif.wait_reset_release();
        `uvm_info("APB_DRIVER", "Reset released, starting to drive transactions", UVM_LOW)

        forever begin
            seq_item_port.get_next_item(req);
            drive_item(req);
            seq_item_port.item_done();
        end
    endtask

    // 驱动单个事务
    virtual task drive_item(apb_seq_item req);
        `uvm_info("APB_DRIVER", $sformatf("Driving item: %s", req.convert2string()), UVM_MEDIUM)

        // SETUP阶段
        @(vif.driver_cb);
        vif.driver_cb.psel    <= 1'b1;
        vif.driver_cb.penable <= 1'b0;
        vif.driver_cb.pwrite  <= req.pwrite;
        vif.driver_cb.paddr   <= req.paddr;
        vif.driver_cb.pwdata  <= req.pwdata;
        vif.driver_cb.pstrb   <= req.pstrb;

        // ACCESS阶段
        @(vif.driver_cb);
        vif.driver_cb.penable <= 1'b1;

        // 等待pready
        while (!vif.driver_cb.pready) begin
            @(vif.driver_cb);
        end

        // 读取响应数据
        req.prdata  = vif.driver_cb.prdata;
        req.pready  = vif.driver_cb.pready;
        req.pslverr = vif.driver_cb.pslverr;

        // 结束事务
        @(vif.driver_cb);
        vif.driver_cb.psel    <= 1'b0;
        vif.driver_cb.penable <= 1'b0;
        vif.driver_cb.pwrite  <= 1'b0;
        vif.driver_cb.paddr   <= 32'h0;
        vif.driver_cb.pwdata  <= 32'h0;
        vif.driver_cb.pstrb   <= 4'h0;

        `uvm_info("APB_DRIVER", $sformatf("Item driven: %s", req.convert2string()), UVM_MEDIUM)
    endtask

endclass
