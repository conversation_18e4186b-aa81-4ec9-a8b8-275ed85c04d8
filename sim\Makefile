# APB UVM验证平台Makefile

# 默认参数
TEST_NAME ?= apb_base_test
SEED ?= 1
CMODEL ?= 0
VERBOSITY ?= UVM_MEDIUM

# 工具和路径
VCS = vcs
OUT_DIR = out
SIMV = $(OUT_DIR)/simv

# 编译选项
VCS_OPTS = -full64 \
           -sverilog \
           -ntb_opts uvm-1.2 \
           -LDFLAGS -rdynamic \
           -debug_access+all \
           -kdb \
           -lca \
           -P ${VERDI_HOME}/share/PLI/VCS/linux64/novas.tab \
           ${VERDI_HOME}/share/PLI/VCS/linux64/pli.a \
           +define+DUMP_FSDB \
           +fsdbfile+tb_top.fsdb \
           -timescale=1ns/1ps \
           -l $(OUT_DIR)/compile.log

# 仿真选项
SIM_OPTS = +UVM_TESTNAME=$(TEST_NAME) \
           +UVM_VERBOSITY=$(VERBOSITY) \
           +fsdb+autoflush \
           +fsdb+force \
           -l $(OUT_DIR)/sim_$(TEST_NAME)_$(SEED).log

# 文件列表
FILELIST = filelist.f

# 默认目标
all: compile run

# 编译目标
compile:
	@echo "=== Compiling APB UVM Testbench ==="
	@echo "VERDI_HOME = $(VERDI_HOME)"
	@if [ -z "$(VERDI_HOME)" ]; then \
		echo "Warning: VERDI_HOME is not set, FSDB dump may not work"; \
	fi
	@mkdir -p $(OUT_DIR)
	bsub -Is -q analog.q -P analog -n 2 -R "rusage[mem=4] span[hosts=1]" \
	$(VCS) $(VCS_OPTS) -f $(FILELIST) -top tb_top -o $(SIMV)

# 仿真目标
run:
	@echo "=== Running Test: $(TEST_NAME) with SEED: $(SEED) ==="
	@mkdir -p $(OUT_DIR)
	cd $(OUT_DIR) && bsub -Is -q analog.q -P analog -n 2 -R "rusage[mem=4] span[hosts=1]" \
	../$(SIMV) $(SIM_OPTS) +ntb_random_seed=$(SEED)

# 清理目标
clean:
	rm -rf $(OUT_DIR)/* csrc *.log *.vpd *.fsdb *.vcd DVEfiles ucli.key vc_hdrs.h
	rm -rf .vlogansetup.env .vlogansetup.args .synopsys_vss.setup
	rm -rf work* *.shm *.diag novas* verdiLog

# 查看波形
wave:
	@if [ -f $(OUT_DIR)/tb_top.fsdb ]; then \
		echo "Opening FSDB waveform..."; \
		verdi -ssf $(OUT_DIR)/tb_top.fsdb &; \
	elif [ -f $(OUT_DIR)/tb_top.vcd ]; then \
		echo "Opening VCD waveform..."; \
		gtkwave $(OUT_DIR)/tb_top.vcd &; \
	else \
		echo "No waveform file found in $(OUT_DIR)!"; \
	fi

# 回归测试
regression:
	@echo "=== Running Regression Tests ==="
	@$(MAKE) all TEST_NAME=apb_base_test SEED=1
	@$(MAKE) all TEST_NAME=apb_read_write_test SEED=2
	@$(MAKE) all TEST_NAME=apb_base_test SEED=3
	@$(MAKE) all TEST_NAME=apb_read_write_test SEED=4

# 帮助信息
help:
	@echo "APB UVM Testbench Makefile"
	@echo "=========================="
	@echo "Usage:"
	@echo "  make all TEST_NAME=<test> SEED=<seed> CMODEL=<0|1>"
	@echo ""
	@echo "Targets:"
	@echo "  all        - Compile and run (default)"
	@echo "  compile    - Compile with LSF"
	@echo "  run        - Run simulation with LSF"
	@echo "  clean      - Clean all generated files"
	@echo "  wave       - Open waveform viewer"
	@echo "  regression - Run regression tests"
	@echo "  help       - Show this help"
	@echo ""
	@echo "Parameters:"
	@echo "  TEST_NAME  - Test to run (default: apb_base_test)"
	@echo "  SEED       - Random seed (default: 1)"
	@echo "  CMODEL     - C model enable (default: 0)"
	@echo "  VERBOSITY  - UVM verbosity (default: UVM_MEDIUM)"
	@echo ""
	@echo "Available Tests:"
	@echo "  apb_base_test       - Basic write test"
	@echo "  apb_read_write_test - Read/write mixed test"
	@echo ""
	@echo "Note: All compilation and simulation are submitted to LSF queue (analog.q)"

.PHONY: all compile run clean wave regression help
