package apb_pkg;

    import uvm_pkg::*;
    `include "uvm_macros.svh"

    // APB操作类型
    typedef enum {READ, WRITE} apb_op_t;

    // APB参数
    parameter APB_DATA_WIDTH = 32;
    parameter APB_ADDR_WIDTH = 32;
    parameter APB_STRB_WIDTH = APB_DATA_WIDTH/8;

    // 包含所有类定义
    `include "../agents/apb_agent/apb_seq_item.sv"
    `include "../agents/apb_agent/apb_sequencer.sv"
    `include "../agents/apb_agent/apb_driver.sv"
    `include "../agents/apb_agent/apb_monitor.sv"
    `include "../agents/apb_agent/apb_agent.sv"
    `include "../sequences/apb_base_seq.sv"
    `include "../sequences/apb_write_seq.sv"
    `include "../sequences/apb_read_seq.sv"
    `include "../sequences/apb_read_write_seq.sv"
    `include "../env/apb_scoreboard.sv"
    `include "../env/apb_env.sv"

endpackage
